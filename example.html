<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>服务器监控系统 - 示例</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Element Plus CDN -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-plus/dist/index.css"
    />
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <!-- 自定义样式 -->
    <style>
      .server-card {
        border: 1px solid #e5e7eb;
      }

      .server-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 6px;
      }

      .status-online {
        background-color: #10b981;
      }

      .status-offline {
        background-color: #ef4444;
      }

      .status-warning {
        background-color: #f59e0b;
      }

      .progress-bar {
        height: 6px;
        border-radius: 3px;
      }

      .server-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
      }

      @media (min-width: 1024px) {
        .server-grid {
          grid-template-columns: repeat(4, 1fr);
        }
      }

      @media (min-width: 1280px) {
        .server-grid {
          grid-template-columns: repeat(6, 1fr);
        }
      }

      .compact-card {
        min-height: 200px;
      }

      /* Element Plus 进度条伸缩动画 */
      .el-progress-bar__outer {
        animation: progress-pulse 2s ease-in-out infinite;
      }

      @keyframes progress-pulse {
        0%,
        100% {
          transform: scaleX(1);
        }
        50% {
          transform: scaleX(1.02);
        }
      }

      /* 进度条容器动画 */
      .progress-container {
        transition: all 0.3s ease;
      }

      .progress-container:hover {
        transform: scaleY(1.1);
      }
    </style>
  </head>
  <body
    class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen"
  >
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="#"
                class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >仪表板</a
              >
              <a
                href="services-example.html"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >服务管理</a
              >
              <a
                href="system-example.html"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >系统监控</a
              >
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-500" id="current-time"></span>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 页面标题 -->
        <div class="mb-8">
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between"
          >
            <div>
              <h2 class="text-3xl font-bold text-gray-900">服务器监控仪表板</h2>
              <p class="mt-2 text-gray-600">
                实时监控12台服务器的系统状态和性能指标
              </p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
              <!-- 搜索框 -->
              <div class="relative">
                <input
                  type="text"
                  id="search-input"
                  placeholder="搜索服务器..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <!-- 状态过滤 -->
              <select
                id="status-filter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">所有状态</option>
                <option value="online">在线</option>
                <option value="warning">警告</option>
                <option value="offline">离线</option>
              </select>
              <!-- 刷新按钮 -->
              <button
                onclick="forceUpdate()"
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-green-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">在线服务器</p>
                <p class="text-2xl font-bold text-gray-900" id="online-count">
                  10
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">总服务器</p>
                <p class="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-yellow-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">警告</p>
                <p class="text-2xl font-bold text-gray-900" id="warning-count">
                  1
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-purple-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">平均CPU</p>
                <p class="text-2xl font-bold text-gray-900" id="avg-cpu">45%</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 服务器网格 -->
        <div class="server-grid" id="servers-grid">
          <!-- 服务器卡片将通过JavaScript动态生成 -->
        </div>
      </div>
    </main>

    <!-- 服务器详情模态框 -->
    <div
      id="server-modal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
      >
        <div class="mt-3">
          <!-- 模态框标题 -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-gray-900" id="modal-server-name">
              服务器详情
            </h3>
            <button
              onclick="closeModal()"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>

          <!-- 服务器基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">基本信息</h4>
              <div class="space-y-2 text-sm">
                <div>
                  <span class="font-medium">IP地址:</span>
                  <span id="modal-server-ip"></span>
                </div>
                <div>
                  <span class="font-medium">主机名:</span>
                  <span id="modal-server-hostname"></span>
                </div>
                <div>
                  <span class="font-medium">操作系统:</span>
                  <span id="modal-server-os"></span>
                </div>
                <div>
                  <span class="font-medium">状态:</span>
                  <span id="modal-server-status"></span>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">性能指标</h4>
              <div class="space-y-3">
                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium">CPU使用率</span>
                    <span class="text-sm" id="modal-cpu-percent">0%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
                      id="modal-cpu-bar"
                      style="width: 0%"
                    ></div>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium">内存使用率</span>
                    <span class="text-sm" id="modal-memory-percent">0%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full"
                      id="modal-memory-bar"
                      style="width: 0%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 控制面板 -->
          <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <h4 class="font-semibold text-gray-700 mb-3">控制面板</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
              <button
                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                重启服务器
              </button>
              <button
                class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                查看日志
              </button>
              <button
                class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                系统信息
              </button>
              <button
                class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                服务管理
              </button>
            </div>
          </div>

          <!-- README和TODO链接 -->
          <div class="flex space-x-4">
            <a
              href="readme-todo-example.html#readme"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >📖 README</a
            >
            <a
              href="readme-todo-example.html#todo"
              class="text-green-600 hover:text-green-800 text-sm font-medium"
              >📝 TODO</a
            >
            <a
              href="readme-todo-example.html#config"
              class="text-purple-600 hover:text-purple-800 text-sm font-medium"
              >⚙️ 配置文件</a
            >
          </div>
        </div>
      </div>
    </div>

    <script>
      // 12台服务器的模拟数据
      const serversData = [
        {
          id: 1,
          name: 'Web-Server-01',
          ip: '*************',
          hostname: 'web01.example.com',
          os: 'Ubuntu 20.04',
          status: 'online',
          cpu: 37.903831902583036,
          memory: 53.6501727811942,
          uptime: '2h',
        },
        {
          id: 2,
          name: 'Web-Server-02',
          ip: '*************',
          hostname: 'web02.example.com',
          os: 'Ubuntu 20.04',
          status: 'online',
          cpu: 42.15,
          memory: 55.23,
          uptime: '1d 4h',
        },
        {
          id: 3,
          name: 'DB-Server-01',
          ip: '*************',
          hostname: 'db01.example.com',
          os: 'CentOS 8',
          status: 'online',
          cpu: 78.456,
          memory: 82.789,
          uptime: '3d 12h',
        },
        {
          id: 4,
          name: 'DB-Server-02',
          ip: '*************',
          hostname: 'db02.example.com',
          os: 'CentOS 8',
          status: 'warning',
          cpu: 85.234,
          memory: 91.567,
          uptime: '5h',
        },
        {
          id: 5,
          name: 'Cache-Server-01',
          ip: '192.168.1.301',
          hostname: 'cache01.example.com',
          os: 'Redis 6.2',
          status: 'online',
          cpu: 25.678,
          memory: 45.123,
          uptime: '7d 2h',
        },
        {
          id: 6,
          name: 'Cache-Server-02',
          ip: '192.168.1.302',
          hostname: 'cache02.example.com',
          os: 'Redis 6.2',
          status: 'online',
          cpu: 28.345,
          memory: 48.901,
          uptime: '2d 8h',
        },
        {
          id: 7,
          name: 'API-Server-01',
          ip: '192.168.1.401',
          hostname: 'api01.example.com',
          os: 'Ubuntu 22.04',
          status: 'online',
          cpu: 52.789,
          memory: 63.456,
          uptime: '1d 15h',
        },
        {
          id: 8,
          name: 'API-Server-02',
          ip: '192.168.1.402',
          hostname: 'api02.example.com',
          os: 'Ubuntu 22.04',
          status: 'online',
          cpu: 48.123,
          memory: 59.678,
          uptime: '4d 6h',
        },
        {
          id: 9,
          name: 'Monitor-Server',
          ip: '192.168.1.501',
          hostname: 'monitor.example.com',
          os: 'Ubuntu 20.04',
          status: 'online',
          cpu: 15.234,
          memory: 32.567,
          uptime: '10d 3h',
        },
        {
          id: 10,
          name: 'Backup-Server',
          ip: '192.168.1.601',
          hostname: 'backup.example.com',
          os: 'CentOS 7',
          status: 'online',
          cpu: 8.456,
          memory: 25.789,
          uptime: '15d 7h',
        },
        {
          id: 11,
          name: 'File-Server',
          ip: '192.168.1.701',
          hostname: 'files.example.com',
          os: 'Ubuntu 18.04',
          status: 'online',
          cpu: 12.678,
          memory: 38.234,
          uptime: '6d 11h',
        },
        {
          id: 12,
          name: 'Test-Server',
          ip: '192.168.1.801',
          hostname: 'test.example.com',
          os: 'Debian 11',
          status: 'offline',
          cpu: 0,
          memory: 0,
          uptime: '--',
        },
      ];

      // 生成服务器卡片HTML
      function createServerCard(server) {
        const statusClass =
          server.status === 'online'
            ? 'status-online'
            : server.status === 'offline'
            ? 'status-offline'
            : 'status-warning';

        const statusText =
          server.status === 'online'
            ? '在线'
            : server.status === 'offline'
            ? '离线'
            : '警告';

        const statusTextClass =
          server.status === 'online'
            ? 'text-green-600'
            : server.status === 'offline'
            ? 'text-red-600'
            : 'text-yellow-600';

        const cpuColor =
          server.cpu > 80
            ? 'from-red-400 to-red-600'
            : server.cpu > 60
            ? 'from-yellow-400 to-yellow-600'
            : 'from-green-400 to-green-600';

        const memoryColor =
          server.memory > 80
            ? 'from-red-400 to-red-600'
            : server.memory > 60
            ? 'from-yellow-400 to-yellow-600'
            : 'from-blue-400 to-blue-600';

        return `
                <div class="server-card compact-card bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md"
                     data-server-id="${server.id}"
                     onclick="navigateToDetail(${server.id})">

                    <!-- 服务器标题和状态 -->
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <span class="status-indicator ${statusClass}"></span>
                            <h3 class="text-sm font-semibold text-gray-900 truncate">${
                              server.name
                            }</h3>
                        </div>
                        <span class="text-xs ${statusTextClass} font-medium">
                            ${statusText}
                        </span>
                    </div>

                    <!-- 服务器基本信息 -->
                    <div class="mb-3">
                        <div class="text-xs text-gray-600 truncate">${
                          server.hostname
                        }</div>
                        <div class="text-xs text-gray-500">${server.ip}</div>
                    </div>

                    <!-- CPU 使用率 -->
                    <div class="mb-3">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs font-medium text-gray-700">CPU</span>
                            <span class="text-xs font-bold text-gray-900">${server.cpu.toFixed(
                              1
                            )}%</span>
                        </div>
                        <div id="cpu-progress-${
                          server.id
                        }" class="progress-container"></div>
                    </div>

                    <!-- 内存使用率 -->
                    <div class="mb-3">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs font-medium text-gray-700">内存</span>
                            <span class="text-xs font-bold text-gray-900">${server.memory.toFixed(
                              1
                            )}%</span>
                        </div>
                        <div id="memory-progress-${
                          server.id
                        }" class="progress-container"></div>
                    </div>

                    <!-- 运行时间 -->
                    <div class="text-xs text-gray-500 text-center">
                        运行时间: ${server.uptime}
                    </div>
                </div>
            `;
      }

      // 当前过滤条件
      let currentFilter = {
        search: '',
        status: 'all',
      };

      // 渲染所有服务器卡片
      function renderServers(filteredData = null) {
        const grid = document.getElementById('servers-grid');
        const dataToRender = filteredData || getFilteredServers();

        if (dataToRender.length === 0) {
          grid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">没有找到服务器</h3>
                        <p class="mt-1 text-sm text-gray-500">请尝试调整搜索条件或过滤器</p>
                    </div>
                `;
        } else {
          grid.innerHTML = dataToRender
            .map(server => createServerCard(server))
            .join('');
          // 重新创建进度条
          createProgressBars();
        }
      }

      // 获取过滤后的服务器数据
      function getFilteredServers() {
        return serversData.filter(server => {
          const matchesSearch =
            server.name
              .toLowerCase()
              .includes(currentFilter.search.toLowerCase()) ||
            server.hostname
              .toLowerCase()
              .includes(currentFilter.search.toLowerCase()) ||
            server.ip.includes(currentFilter.search);

          const matchesStatus =
            currentFilter.status === 'all' ||
            server.status === currentFilter.status;

          return matchesSearch && matchesStatus;
        });
      }

      // 搜索功能
      function setupSearch() {
        const searchInput = document.getElementById('search-input');
        const statusFilter = document.getElementById('status-filter');

        searchInput.addEventListener('input', function () {
          currentFilter.search = this.value;
          renderServers();
        });

        statusFilter.addEventListener('change', function () {
          currentFilter.status = this.value;
          renderServers();
        });
      }

      // 强制更新数据
      function forceUpdate() {
        simulateDataUpdate();
      }

      // 更新统计信息
      function updateStats() {
        const onlineServers = serversData.filter(
          s => s.status === 'online'
        ).length;
        const warningServers = serversData.filter(
          s => s.status === 'warning'
        ).length;
        const avgCpu = Math.round(
          serversData.reduce((sum, s) => sum + s.cpu, 0) / serversData.length
        );

        document.getElementById('online-count').textContent = onlineServers;
        document.getElementById('warning-count').textContent = warningServers;
        document.getElementById('avg-cpu').textContent = avgCpu + '%';
      }

      // 模拟数据更新
      function simulateDataUpdate() {
        serversData.forEach(server => {
          if (server.status !== 'offline') {
            // 随机变化CPU和内存使用率
            server.cpu = Math.max(
              5,
              Math.min(95, server.cpu + (Math.random() - 0.5) * 10)
            );
            server.memory = Math.max(
              10,
              Math.min(95, server.memory + (Math.random() - 0.5) * 8)
            );

            // 根据使用率调整状态
            if (server.cpu > 85 || server.memory > 90) {
              server.status = 'warning';
            } else if (
              server.status === 'warning' &&
              server.cpu < 70 &&
              server.memory < 80
            ) {
              server.status = 'online';
            }
          }
        });

        renderServers();
        updateStats();
      }

      // 导航到服务器详情页面
      function navigateToDetail(serverId) {
        const server = serversData.find(s => s.id === serverId);
        if (server) {
          showServerModal(server);
        }
      }

      // 显示服务器详情模态框
      function showServerModal(server) {
        document.getElementById('modal-server-name').textContent = server.name;
        document.getElementById('modal-server-ip').textContent = server.ip;
        document.getElementById('modal-server-hostname').textContent =
          server.hostname;
        document.getElementById('modal-server-os').textContent = server.os;

        const statusText =
          server.status === 'online'
            ? '在线'
            : server.status === 'offline'
            ? '离线'
            : '警告';
        const statusClass =
          server.status === 'online'
            ? 'text-green-600'
            : server.status === 'offline'
            ? 'text-red-600'
            : 'text-yellow-600';

        const statusElement = document.getElementById('modal-server-status');
        statusElement.textContent = statusText;
        statusElement.className = statusClass;

        // 更新性能指标
        document.getElementById('modal-cpu-percent').textContent =
          server.cpu + '%';
        document.getElementById('modal-memory-percent').textContent =
          server.memory + '%';
        document.getElementById('modal-cpu-bar').style.width = server.cpu + '%';
        document.getElementById('modal-memory-bar').style.width =
          server.memory + '%';

        // 根据使用率调整进度条颜色
        const cpuBar = document.getElementById('modal-cpu-bar');
        const memoryBar = document.getElementById('modal-memory-bar');

        if (server.cpu > 80) {
          cpuBar.className =
            'bg-gradient-to-r from-red-400 to-red-600 h-2 rounded-full';
        } else if (server.cpu > 60) {
          cpuBar.className =
            'bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full';
        } else {
          cpuBar.className =
            'bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full';
        }

        if (server.memory > 80) {
          memoryBar.className =
            'bg-gradient-to-r from-red-400 to-red-600 h-2 rounded-full';
        } else if (server.memory > 60) {
          memoryBar.className =
            'bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full';
        } else {
          memoryBar.className =
            'bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full';
        }

        // 显示模态框
        document.getElementById('server-modal').classList.remove('hidden');
      }

      // 关闭模态框
      function closeModal() {
        document.getElementById('server-modal').classList.add('hidden');
      }

      // 点击模态框外部关闭
      document.addEventListener('click', function (event) {
        const modal = document.getElementById('server-modal');
        if (event.target === modal) {
          closeModal();
        }
      });

      // 更新当前时间
      function updateTime() {
        document.getElementById('current-time').textContent =
          new Date().toLocaleString();
      }

      // 创建Element Plus进度条
      function createProgressBars() {
        const servers = getFilteredServers();
        servers.forEach(server => {
          // 创建CPU进度条
          const cpuContainer = document.getElementById(
            `cpu-progress-${server.id}`
          );
          if (cpuContainer) {
            const cpuColor =
              server.cpu > 80
                ? '#f56c6c'
                : server.cpu > 60
                ? '#e6a23c'
                : '#67c23a';
            const cpuApp = Vue.createApp({
              template: `
                <el-progress
                  :percentage="${server.cpu}"
                  :color="'${cpuColor}'"
                  :format="(percentage) => percentage.toFixed(1) + '%'"
                    :text-inside="true"
      :stroke-width="24"
      :percentage="100"
      status="success"
                ></el-progress>
              `,
            });
            cpuApp.use(ElementPlus);
            cpuApp.mount(cpuContainer);
          }

          // 创建内存进度条
          const memoryContainer = document.getElementById(
            `memory-progress-${server.id}`
          );
          if (memoryContainer) {
            const memoryColor =
              server.memory > 80
                ? '#f56c6c'
                : server.memory > 60
                ? '#e6a23c'
                : '#409eff';
            const memoryApp = Vue.createApp({
              template: `
                <el-progress
                  :percentage="${server.memory}"
                  :color="'${memoryColor}'"
                  :format="(percentage) => percentage.toFixed(1) + '%'"
                    :text-inside="true"
      :stroke-width="24"
      :percentage="100"
      status="success"
                ></el-progress>
              `,
            });
            memoryApp.use(ElementPlus);
            memoryApp.mount(memoryContainer);
          }
        });
      }

      // 页面初始化
      document.addEventListener('DOMContentLoaded', function () {
        console.log('Dashboard example loaded');

        // 初始渲染
        renderServers();
        createProgressBars();
        updateStats();
        updateTime();
        setupSearch();

        // 每5秒更新数据
        setInterval(simulateDataUpdate, 5000);

        // 每秒更新时间
        setInterval(updateTime, 1000);

        // ESC键关闭模态框
        document.addEventListener('keydown', function (event) {
          if (event.key === 'Escape') {
            closeModal();
          }
        });

        console.log('所有功能已初始化完成');
      });
    </script>
  </body>
</html>
