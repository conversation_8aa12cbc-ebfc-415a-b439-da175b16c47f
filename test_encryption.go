package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"

	"golang.org/x/crypto/pbkdf2"
)

// Test data structure
type TestData struct {
	Message   string  `json:"message"`
	Number    int     `json:"number"`
	FloatVal  float64 `json:"float_val"`
	Timestamp string  `json:"timestamp"`
}

// Encryption constants
const (
	keySize    = 16    // AES-128
	nonceSize  = 12    // GCM nonce size
	saltSize   = 16    // PBKDF2 salt size
	iterations = 10000 // PBKDF2 iterations
)

// derive<PERSON>ey derives a key from password using PBKDF2
func deriveKey(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)
}

// encrypt encrypts data using AES-128-GCM
func encrypt(data []byte, password string) (string, error) {
	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %v", err)
	}

	// Derive key from password
	key := deriveKey(password, salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Encrypt data
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// Combine salt + nonce + ciphertext
	result := make([]byte, saltSize+nonceSize+len(ciphertext))
	copy(result[:saltSize], salt)
	copy(result[saltSize:saltSize+nonceSize], nonce)
	copy(result[saltSize+nonceSize:], ciphertext)

	// Return base64 encoded result
	return base64.StdEncoding.EncodeToString(result), nil
}

// decrypt decrypts data using AES-128-GCM
func decrypt(encryptedData string, password string) ([]byte, error) {
	// Decode base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// Check minimum length
	if len(data) < saltSize+nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// Extract salt, nonce, and ciphertext
	salt := data[:saltSize]
	nonce := data[saltSize : saltSize+nonceSize]
	ciphertext := data[saltSize+nonceSize:]

	// Derive key from password
	key := deriveKey(password, salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	// Decrypt data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %v", err)
	}

	return plaintext, nil
}

func main() {
	fmt.Println("🔐 AES-128-GCM Encryption Test")
	fmt.Println("==============================")

	// Test data
	testData := TestData{
		Message:   "Hello, this is a secret message!",
		Number:    12345,
		FloatVal:  98.76,
		Timestamp: "2025-07-31T15:30:00Z",
	}

	password := "VPS_Monitor_2024_SecureAuth"

	// Convert to JSON
	jsonData, err := json.Marshal(testData)
	if err != nil {
		log.Fatalf("Failed to marshal test data: %v", err)
	}

	fmt.Printf("📝 Original data: %s\n", string(jsonData))
	fmt.Printf("🔑 Password: %s\n", password)
	fmt.Println()

	// Encrypt
	fmt.Println("🔒 Encrypting...")
	encryptedData, err := encrypt(jsonData, password)
	if err != nil {
		log.Fatalf("Encryption failed: %v", err)
	}

	fmt.Printf("✅ Encrypted data (base64): %s\n", encryptedData)
	fmt.Printf("📏 Encrypted data length: %d bytes\n", len(encryptedData))
	fmt.Println()

	// Decrypt
	fmt.Println("🔓 Decrypting...")
	decryptedData, err := decrypt(encryptedData, password)
	if err != nil {
		log.Fatalf("Decryption failed: %v", err)
	}

	fmt.Printf("✅ Decrypted data: %s\n", string(decryptedData))
	fmt.Println()

	// Verify
	var decryptedTestData TestData
	err = json.Unmarshal(decryptedData, &decryptedTestData)
	if err != nil {
		log.Fatalf("Failed to unmarshal decrypted data: %v", err)
	}

	fmt.Println("🔍 Verification:")
	fmt.Printf("   Original message: %s\n", testData.Message)
	fmt.Printf("   Decrypted message: %s\n", decryptedTestData.Message)
	fmt.Printf("   Original number: %d\n", testData.Number)
	fmt.Printf("   Decrypted number: %d\n", decryptedTestData.Number)
	fmt.Printf("   Original float: %.2f\n", testData.FloatVal)
	fmt.Printf("   Decrypted float: %.2f\n", decryptedTestData.FloatVal)

	if testData.Message == decryptedTestData.Message &&
		testData.Number == decryptedTestData.Number &&
		testData.FloatVal == decryptedTestData.FloatVal {
		fmt.Println("✅ SUCCESS: Encryption/Decryption test passed!")
	} else {
		fmt.Println("❌ FAILED: Data mismatch!")
	}

	// Test with wrong password
	fmt.Println()
	fmt.Println("🚫 Testing with wrong password...")
	wrongPassword := "wrong_password"
	_, err = decrypt(encryptedData, wrongPassword)
	if err != nil {
		fmt.Printf("✅ Expected error with wrong password: %v\n", err)
	} else {
		fmt.Println("❌ ERROR: Decryption should have failed with wrong password!")
	}

	fmt.Println()
	fmt.Println("🎉 AES-128-GCM encryption test completed!")
}
