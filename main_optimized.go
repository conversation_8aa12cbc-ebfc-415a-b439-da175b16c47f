package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
	"github.com/shirou/gopsutil/v4/net"
	"golang.org/x/crypto/pbkdf2"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	_ "modernc.org/sqlite"
)

// Configuration structures
type ServerConfig struct {
	Listen  string `json:"listen"`
	Port    string `json:"port"`
	Servers []struct {
		ID      int    `json:"id"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Password string `json:"password"`
}

type ClientConfig struct {
	MID       int    `json:"mid"`
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password   string `json:"password"`
	ServerInfo struct {
		Name         string `json:"name"`
		Tag          string `json:"tag"`
		IPv4         string `json:"ipv4"`
		IPv6         string `json:"ipv6"`
		AutoDetectIP bool   `json:"auto_detect_ip"`
	} `json:"server_info"`
}

// Host information structure
type HostInfo struct {
	Platform        string `json:"Platform"`
	PlatformVersion string `json:"PlatformVersion"`
	CPU             string `json:"CPU" gorm:"type:text"` // Store as JSON string
	MemTotal        uint64 `json:"MemTotal"`
	DiskTotal       uint64 `json:"DiskTotal"`
	SwapTotal       uint64 `json:"SwapTotal"`
	Arch            string `json:"Arch"`
	Virtualization  string `json:"Virtualization"`
	BootTime        int64  `json:"BootTime"`
	CountryCode     string `json:"CountryCode"`
	Version         string `json:"Version"`
}

// Status information structure
type StatusInfo struct {
	CPU            float64 `json:"CPU"`
	MemUsed        uint64  `json:"MemUsed"`
	SwapUsed       uint64  `json:"SwapUsed"`
	DiskUsed       uint64  `json:"DiskUsed"`
	NetInTransfer  uint64  `json:"NetInTransfer"`
	NetOutTransfer uint64  `json:"NetOutTransfer"`
	NetInSpeed     uint64  `json:"NetInSpeed"`
	NetOutSpeed    uint64  `json:"NetOutSpeed"`
	Uptime         uint64  `json:"Uptime"`
	Load1          float64 `json:"Load1"`
	Load5          float64 `json:"Load5"`
	Load15         float64 `json:"Load15"`
	TcpConnCount   int     `json:"TcpConnCount"`
	UdpConnCount   int     `json:"UdpConnCount"`
	ProcessCount   int     `json:"ProcessCount"`
}

// Complete server details structure
type ServerDetails struct {
	ID         int        `json:"id" gorm:"primaryKey"`
	Name       string     `json:"name"`
	Tag        string     `json:"tag"`
	LastActive int64      `json:"last_active"`
	IPv4       string     `json:"ipv4"`
	IPv6       string     `json:"ipv6"`
	ValidIP    string     `json:"valid_ip"`
	Host       HostInfo   `json:"host" gorm:"embedded;embeddedPrefix:host_"`
	Status     StatusInfo `json:"status" gorm:"embedded;embeddedPrefix:status_"`
	gorm.Model
}

// WebSocket message structure
type Message struct {
	Type          string        `json:"type"`
	Password      string        `json:"password"`
	Data          ServerDetails `json:"data,omitempty"`
	EncryptedData string        `json:"encrypted_data,omitempty"`
	Encrypted     bool          `json:"encrypted"`
	Timestamp     time.Time     `json:"timestamp"`
}

var (
	isClient   = flag.Bool("c", false, "Run as client")
	isServer   = flag.Bool("s", false, "Run as server")
	configFile = flag.String("f", "", "Configuration file path")
	db         *gorm.DB
	upgrader   = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for simplicity
		},
	}
)

func main() {
	flag.Parse()

	if !*isClient && !*isServer {
		fmt.Println("Please specify either -c (client) or -s (server)")
		flag.Usage()
		os.Exit(1)
	}

	if *configFile == "" {
		if *isServer {
			*configFile = "server.json"
		} else {
			*configFile = "client.json"
		}
	}

	if *isServer {
		runServer()
	} else {
		runClient()
	}
}

func runServer() {
	fmt.Println("Starting server mode...")

	config, err := loadServerConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load server config: %v", err)
	}

	// Initialize database
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "server.db"
	}
	initDatabase(dbPath)

	// Setup WebSocket handler
	http.HandleFunc(config.WebSocket.Path, func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(w, r, config.Password)
	})

	// Setup API endpoints
	http.HandleFunc("/api/servers", getServersAPI)
	http.HandleFunc("/", serveIndex)

	// Start HTTP server
	addr := fmt.Sprintf("%s:%s", config.Listen, config.Port)
	fmt.Printf("Server listening on %s%s\n", addr, config.WebSocket.Path)
	fmt.Printf("Web interface available at http://%s/\n", addr)
	fmt.Printf("API endpoint available at http://%s/api/servers\n", addr)
	log.Fatal(http.ListenAndServe(addr, nil))
}

func runClient() {
	fmt.Println("Starting client mode...")

	config, err := loadClientConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load client config: %v", err)
	}

	// Initialize local database
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "client.db"
	}
	initDatabase(dbPath)

	// Connect to server and start monitoring
	connectAndMonitor(config)
}

func loadServerConfig(filename string) (*ServerConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ServerConfig
	err = json.Unmarshal(data, &config)
	return &config, err
}

func loadClientConfig(filename string) (*ClientConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ClientConfig
	err = json.Unmarshal(data, &config)
	return &config, err
}

func initDatabase(dbPath string) {
	var err error
	db, err = gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        dbPath,
	}, &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Auto migrate the schema
	err = db.AutoMigrate(&ServerDetails{})
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	fmt.Printf("Database initialized: %s\n", dbPath)
}

func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	fmt.Printf("Client connected: %s\n", r.RemoteAddr)

	for {
		var msg Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("Read error: %v", err)
			break
		}

		// Verify password
		if msg.Password != password {
			log.Printf("Invalid password from %s", r.RemoteAddr)
			continue
		}

		var serverDetails ServerDetails

		if msg.Encrypted && msg.EncryptedData != "" {
			// Decrypt data
			decryptedData, err := decrypt(msg.EncryptedData, password)
			if err != nil {
				log.Printf("Failed to decrypt data from %s: %v", r.RemoteAddr, err)
				continue
			}

			// Unmarshal decrypted data
			err = json.Unmarshal(decryptedData, &serverDetails)
			if err != nil {
				log.Printf("Failed to unmarshal decrypted data from %s: %v", r.RemoteAddr, err)
				continue
			}
		} else {
			// Use unencrypted data (backward compatibility)
			serverDetails = msg.Data
		}

		// Store data in database
		serverDetails.LastActive = time.Now().Unix()

		// Upsert: update if exists, insert if not
		result := db.Save(&serverDetails)
		if result.Error != nil {
			log.Printf("Database error: %v", result.Error)
		} else {
			encStatus := ""
			if msg.Encrypted {
				encStatus = " [ENCRYPTED]"
			}
			fmt.Printf("Stored data from Server[%d:%s]: CPU=%.2f%%, Mem=%.2fGB%%%s\n",
				serverDetails.ID, serverDetails.Name, serverDetails.Status.CPU,
				float64(serverDetails.Status.MemUsed)/1024/1024/1024, encStatus)
		}
	}

	fmt.Printf("Client disconnected: %s\n", r.RemoteAddr)
}

// API handler to get all servers data
func getServersAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	var servers []ServerDetails
	result := db.Find(&servers)
	if result.Error != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	// Convert CPU JSON strings back to arrays for display
	for i := range servers {
		var cpuArray []string
		if err := json.Unmarshal([]byte(servers[i].Host.CPU), &cpuArray); err == nil {
			// Create a temporary struct for JSON response
			servers[i].Host.CPU = strings.Join(cpuArray, ", ")
		}
	}

	json.NewEncoder(w).Encode(servers)
}

// Serve the index.html file
func serveIndex(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}
	http.ServeFile(w, r, "index.html")
}

func connectAndMonitor(config *ClientConfig) {
	url := fmt.Sprintf("ws://%s:%s%s", config.Server, config.Port, config.WebSocket.Path)

	for {
		fmt.Printf("Connecting to %s...\n", url)

		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			log.Printf("Connection failed: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		fmt.Println("Connected to server")

		// Start monitoring loop
		monitorLoop(conn, config.Password, config)

		conn.Close()
		fmt.Println("Disconnected, retrying in 5 seconds...")
		time.Sleep(5 * time.Second)
	}
}

func monitorLoop(conn *websocket.Conn, password string, config *ClientConfig) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			serverDetails, err := collectServerDetails(config)
			if err != nil {
				log.Printf("Failed to collect server details: %v", err)
				continue
			}

			// Store locally (create a copy without fixed ID for local storage)
			localDetails := *serverDetails
			localDetails.ID = 0 // Let database auto-generate ID for local storage
			db.Create(&localDetails)

			// Encrypt server details
			serverDetailsJSON, err := json.Marshal(serverDetails)
			if err != nil {
				log.Printf("Failed to marshal server details: %v", err)
				continue
			}

			encryptedData, err := encrypt(serverDetailsJSON, password)
			if err != nil {
				log.Printf("Failed to encrypt data: %v", err)
				continue
			}

			// Send to server
			msg := Message{
				Type:          "system_info",
				Password:      password,
				EncryptedData: encryptedData,
				Encrypted:     true,
				Timestamp:     time.Now(),
			}

			err = conn.WriteJSON(msg)
			if err != nil {
				log.Printf("Send error: %v", err)
				return
			}

			fmt.Printf("Sent Server[%d:%s]: CPU=%.2f%%, Mem=%.2fGB\n",
				serverDetails.ID, serverDetails.Name, serverDetails.Status.CPU,
				float64(serverDetails.Status.MemUsed)/1024/1024/1024)
		}
	}
}

func collectServerDetails(config *ClientConfig) (*ServerDetails, error) {
	serverDetails := &ServerDetails{
		ID:   config.MID, // Use MID from config for server identification
		Name: config.ServerInfo.Name,
		Tag:  config.ServerInfo.Tag,
	}

	// Auto-detect IP if enabled
	if config.ServerInfo.AutoDetectIP {
		// This would be implemented with actual IP detection logic
		serverDetails.IPv4 = "127.0.0.1" // Placeholder
		serverDetails.ValidIP = serverDetails.IPv4
	} else {
		serverDetails.IPv4 = config.ServerInfo.IPv4
		serverDetails.IPv6 = config.ServerInfo.IPv6
		if serverDetails.IPv4 != "" {
			serverDetails.ValidIP = serverDetails.IPv4
		} else {
			serverDetails.ValidIP = serverDetails.IPv6
		}
	}

	// Get host information
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("failed to get host info: %v", err)
	}

	// Get CPU info
	cpuInfo, err := cpu.Info()
	var cpuNames []string
	if err == nil && len(cpuInfo) > 0 {
		cpuNames = []string{cpuInfo[0].ModelName}
	} else {
		cpuNames = []string{"Unknown CPU"}
	}

	// Get memory info
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get memory info: %v", err)
	}

	// Get disk info
	diskInfo, err := disk.Usage("/")
	if err != nil {
		diskInfo, err = disk.Usage("C:")
		if err != nil {
			return nil, fmt.Errorf("failed to get disk info: %v", err)
		}
	}

	// Convert CPU names to JSON string
	cpuJSON, _ := json.Marshal(cpuNames)

	// Fill host information
	serverDetails.Host = HostInfo{
		Platform:        hostInfo.Platform,
		PlatformVersion: hostInfo.PlatformVersion,
		CPU:             string(cpuJSON),
		MemTotal:        memInfo.Total,
		DiskTotal:       diskInfo.Total,
		SwapTotal:       memInfo.SwapTotal,
		Arch:            hostInfo.KernelArch,
		Virtualization:  hostInfo.VirtualizationSystem,
		BootTime:        int64(hostInfo.BootTime),
		CountryCode:     "auto", // Would be detected in real implementation
		Version:         hostInfo.KernelVersion,
	}

	// Get current status
	cpuPercent, err := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if err == nil && len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// Get load average
	loadInfo, err := load.Avg()
	var load1, load5, load15 float64
	if err == nil {
		load1 = loadInfo.Load1
		load5 = loadInfo.Load5
		load15 = loadInfo.Load15
	}

	// Get network I/O
	netInfo, err := net.IOCounters(false)
	var netIn, netOut uint64
	if err == nil && len(netInfo) > 0 {
		netIn = netInfo[0].BytesRecv
		netOut = netInfo[0].BytesSent
	}

	// Get swap info
	swapInfo, _ := mem.SwapMemory()
	var swapUsed uint64
	if swapInfo != nil {
		swapUsed = swapInfo.Used
	}

	// Fill status information
	serverDetails.Status = StatusInfo{
		CPU:            cpuUsage,
		MemUsed:        memInfo.Used,
		SwapUsed:       swapUsed,
		DiskUsed:       diskInfo.Used,
		NetInTransfer:  netIn,
		NetOutTransfer: netOut,
		NetInSpeed:     0, // Would need to calculate speed
		NetOutSpeed:    0, // Would need to calculate speed
		Uptime:         uint64(time.Now().Unix() - int64(hostInfo.BootTime)),
		Load1:          load1,
		Load5:          load5,
		Load15:         load15,
		TcpConnCount:   0, // Would need additional implementation
		UdpConnCount:   0, // Would need additional implementation
		ProcessCount:   int(hostInfo.Procs),
	}

	return serverDetails, nil
}

// Encryption functions
const (
	keySize    = 16    // AES-128
	nonceSize  = 12    // GCM nonce size
	saltSize   = 16    // PBKDF2 salt size
	iterations = 10000 // PBKDF2 iterations
)

// deriveKey derives a key from password using PBKDF2
func deriveKey(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)
}

// encrypt encrypts data using AES-128-GCM
func encrypt(data []byte, password string) (string, error) {
	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %v", err)
	}

	// Derive key from password
	key := deriveKey(password, salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Encrypt data
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// Combine salt + nonce + ciphertext
	result := make([]byte, saltSize+nonceSize+len(ciphertext))
	copy(result[:saltSize], salt)
	copy(result[saltSize:saltSize+nonceSize], nonce)
	copy(result[saltSize+nonceSize:], ciphertext)

	// Return base64 encoded result
	return base64.StdEncoding.EncodeToString(result), nil
}

// decrypt decrypts data using AES-128-GCM
func decrypt(encryptedData string, password string) ([]byte, error) {
	// Decode base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// Check minimum length
	if len(data) < saltSize+nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// Extract salt, nonce, and ciphertext
	salt := data[:saltSize]
	nonce := data[saltSize : saltSize+nonceSize]
	ciphertext := data[saltSize+nonceSize:]

	// Derive key from password
	key := deriveKey(password, salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	// Decrypt data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %v", err)
	}

	return plaintext, nil
}
