package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
	"github.com/shirou/gopsutil/v4/net"
	"golang.org/x/crypto/pbkdf2"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	_ "modernc.org/sqlite"
)

// Configuration structures
type ServerConfig struct {
	Listen  string `json:"listen"`
	Port    string `json:"port"`
	Servers []struct {
		ID      int    `json:"id"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Password string `json:"password"`
}

type ClientConfig struct {
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password string `json:"password"`
}

// System information structure
type SystemInfo struct {
	Timestamp   time.Time `json:"timestamp"`
	Hostname    string    `json:"hostname"`
	Platform    string    `json:"platform"`
	CPU         float64   `json:"cpu_percent"`
	MemTotal    uint64    `json:"mem_total"`
	MemUsed     uint64    `json:"mem_used"`
	MemPercent  float64   `json:"mem_percent"`
	DiskTotal   uint64    `json:"disk_total"`
	DiskUsed    uint64    `json:"disk_used"`
	DiskPercent float64   `json:"disk_percent"`
	Load1       float64   `json:"load1"`
	Load5       float64   `json:"load5"`
	Load15      float64   `json:"load15"`
	NetIn       uint64    `json:"net_in"`
	NetOut      uint64    `json:"net_out"`
}

// WebSocket message structure
type Message struct {
	Type          string     `json:"type"`
	Password      string     `json:"password"`
	Data          SystemInfo `json:"data,omitempty"`
	EncryptedData string     `json:"encrypted_data,omitempty"`
	Encrypted     bool       `json:"encrypted"`
	Timestamp     time.Time  `json:"timestamp"`
}

var (
	isClient   = flag.Bool("c", false, "Run as client")
	isServer   = flag.Bool("s", false, "Run as server")
	configFile = flag.String("f", "", "Configuration file path")
	db         *gorm.DB
	upgrader   = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for simplicity
		},
	}
)

func main() {
	flag.Parse()

	if !*isClient && !*isServer {
		fmt.Println("Please specify either -c (client) or -s (server)")
		flag.Usage()
		os.Exit(1)
	}

	if *configFile == "" {
		if *isServer {
			*configFile = "server.json"
		} else {
			*configFile = "client.json"
		}
	}

	if *isServer {
		runServer()
	} else {
		runClient()
	}
}

func runServer() {
	fmt.Println("Starting server mode...")

	config, err := loadServerConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load server config: %v", err)
	}

	// Initialize database
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "server.db"
	}
	initDatabase(dbPath)

	// Setup WebSocket handler
	http.HandleFunc(config.WebSocket.Path, func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(w, r, config.Password)
	})

	// Start HTTP server
	addr := fmt.Sprintf("%s:%s", config.Listen, config.Port)
	fmt.Printf("Server listening on %s%s\n", addr, config.WebSocket.Path)
	log.Fatal(http.ListenAndServe(addr, nil))
}

func runClient() {
	fmt.Println("Starting client mode...")

	config, err := loadClientConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load client config: %v", err)
	}

	// Initialize local database
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "client.db"
	}
	initDatabase(dbPath)

	// Connect to server and start monitoring
	connectAndMonitor(config)
}

func loadServerConfig(filename string) (*ServerConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ServerConfig
	err = json.Unmarshal(data, &config)
	return &config, err
}

func loadClientConfig(filename string) (*ClientConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ClientConfig
	err = json.Unmarshal(data, &config)
	return &config, err
}

func initDatabase(dbPath string) {
	var err error
	db, err = gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        dbPath,
	}, &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Auto migrate the schema
	err = db.AutoMigrate(&SystemInfo{})
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	fmt.Printf("Database initialized: %s\n", dbPath)
}

func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	fmt.Printf("Client connected: %s\n", r.RemoteAddr)

	for {
		var msg Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("Read error: %v", err)
			break
		}

		// Verify password
		if msg.Password != password {
			log.Printf("Invalid password from %s", r.RemoteAddr)
			continue
		}

		var sysInfo SystemInfo

		if msg.Encrypted && msg.EncryptedData != "" {
			// Decrypt data
			decryptedData, err := decrypt(msg.EncryptedData, password)
			if err != nil {
				log.Printf("Failed to decrypt data from %s: %v", r.RemoteAddr, err)
				continue
			}

			// Unmarshal decrypted data
			err = json.Unmarshal(decryptedData, &sysInfo)
			if err != nil {
				log.Printf("Failed to unmarshal decrypted data from %s: %v", r.RemoteAddr, err)
				continue
			}
		} else {
			// Use unencrypted data (backward compatibility)
			sysInfo = msg.Data
		}

		// Store data in database
		sysInfo.Timestamp = time.Now()
		result := db.Create(&sysInfo)
		if result.Error != nil {
			log.Printf("Database error: %v", result.Error)
		} else {
			encStatus := ""
			if msg.Encrypted {
				encStatus = " [ENCRYPTED]"
			}
			fmt.Printf("Stored data from %s: CPU=%.2f%%, Mem=%.2f%%%s\n",
				sysInfo.Hostname, sysInfo.CPU, sysInfo.MemPercent, encStatus)
		}
	}

	fmt.Printf("Client disconnected: %s\n", r.RemoteAddr)
}

func connectAndMonitor(config *ClientConfig) {
	url := fmt.Sprintf("ws://%s:%s%s", config.Server, config.Port, config.WebSocket.Path)

	for {
		fmt.Printf("Connecting to %s...\n", url)

		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			log.Printf("Connection failed: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		fmt.Println("Connected to server")

		// Start monitoring loop
		monitorLoop(conn, config.Password)

		conn.Close()
		fmt.Println("Disconnected, retrying in 5 seconds...")
		time.Sleep(5 * time.Second)
	}
}

func monitorLoop(conn *websocket.Conn, password string) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sysInfo, err := collectSystemInfo()
			if err != nil {
				log.Printf("Failed to collect system info: %v", err)
				continue
			}

			// Store locally
			sysInfo.Timestamp = time.Now()
			db.Create(&sysInfo)

			// Encrypt system info
			sysInfoJSON, err := json.Marshal(sysInfo)
			if err != nil {
				log.Printf("Failed to marshal system info: %v", err)
				continue
			}

			encryptedData, err := encrypt(sysInfoJSON, password)
			if err != nil {
				log.Printf("Failed to encrypt data: %v", err)
				continue
			}

			// Send to server
			msg := Message{
				Type:          "system_info",
				Password:      password,
				EncryptedData: encryptedData,
				Encrypted:     true,
				Timestamp:     time.Now(),
			}

			err = conn.WriteJSON(msg)
			if err != nil {
				log.Printf("Send error: %v", err)
				return
			}

			fmt.Printf("Sent: CPU=%.2f%%, Mem=%.2f%%, Disk=%.2f%%\n",
				sysInfo.CPU, sysInfo.MemPercent, sysInfo.DiskPercent)
		}
	}
}

func collectSystemInfo() (*SystemInfo, error) {
	sysInfo := &SystemInfo{}

	// Get hostname
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("failed to get host info: %v", err)
	}
	sysInfo.Hostname = hostInfo.Hostname
	sysInfo.Platform = hostInfo.Platform

	// Get CPU usage
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get CPU usage: %v", err)
	}
	if len(cpuPercent) > 0 {
		sysInfo.CPU = cpuPercent[0]
	}

	// Get memory usage
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get memory info: %v", err)
	}
	sysInfo.MemTotal = memInfo.Total
	sysInfo.MemUsed = memInfo.Used
	sysInfo.MemPercent = memInfo.UsedPercent

	// Get disk usage (root partition)
	diskInfo, err := disk.Usage("/")
	if err != nil {
		// Try Windows C: drive
		diskInfo, err = disk.Usage("C:")
		if err != nil {
			return nil, fmt.Errorf("failed to get disk info: %v", err)
		}
	}
	sysInfo.DiskTotal = diskInfo.Total
	sysInfo.DiskUsed = diskInfo.Used
	sysInfo.DiskPercent = diskInfo.UsedPercent

	// Get load average
	loadInfo, err := load.Avg()
	if err == nil {
		sysInfo.Load1 = loadInfo.Load1
		sysInfo.Load5 = loadInfo.Load5
		sysInfo.Load15 = loadInfo.Load15
	}

	// Get network I/O
	netInfo, err := net.IOCounters(false)
	if err == nil && len(netInfo) > 0 {
		sysInfo.NetIn = netInfo[0].BytesRecv
		sysInfo.NetOut = netInfo[0].BytesSent
	}

	return sysInfo, nil
}

// Encryption functions
const (
	keySize    = 16    // AES-128
	nonceSize  = 12    // GCM nonce size
	saltSize   = 16    // PBKDF2 salt size
	iterations = 10000 // PBKDF2 iterations
)

// deriveKey derives a key from password using PBKDF2
func deriveKey(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)
}

// encrypt encrypts data using AES-128-GCM
func encrypt(data []byte, password string) (string, error) {
	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %v", err)
	}

	// Derive key from password
	key := deriveKey(password, salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Encrypt data
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// Combine salt + nonce + ciphertext
	result := make([]byte, saltSize+nonceSize+len(ciphertext))
	copy(result[:saltSize], salt)
	copy(result[saltSize:saltSize+nonceSize], nonce)
	copy(result[saltSize+nonceSize:], ciphertext)

	// Return base64 encoded result
	return base64.StdEncoding.EncodeToString(result), nil
}

// decrypt decrypts data using AES-128-GCM
func decrypt(encryptedData string, password string) ([]byte, error) {
	// Decode base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// Check minimum length
	if len(data) < saltSize+nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// Extract salt, nonce, and ciphertext
	salt := data[:saltSize]
	nonce := data[saltSize : saltSize+nonceSize]
	ciphertext := data[saltSize+nonceSize:]

	// Derive key from password
	key := deriveKey(password, salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	// Decrypt data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %v", err)
	}

	return plaintext, nil
}
