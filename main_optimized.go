package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
	"github.com/shirou/gopsutil/v4/net"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	_ "modernc.org/sqlite"
)

// Configuration structures
type ServerConfig struct {
	Listen  string `json:"listen"`
	Port    string `json:"port"`
	Servers []struct {
		ID      int    `json:"id"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Password string `json:"password"`
}

type ClientConfig struct {
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password string `json:"password"`
}

// System information structure
type SystemInfo struct {
	Timestamp   time.Time `json:"timestamp"`
	Hostname    string    `json:"hostname"`
	Platform    string    `json:"platform"`
	CPU         float64   `json:"cpu_percent"`
	MemTotal    uint64    `json:"mem_total"`
	MemUsed     uint64    `json:"mem_used"`
	MemPercent  float64   `json:"mem_percent"`
	DiskTotal   uint64    `json:"disk_total"`
	DiskUsed    uint64    `json:"disk_used"`
	DiskPercent float64   `json:"disk_percent"`
	Load1       float64   `json:"load1"`
	Load5       float64   `json:"load5"`
	Load15      float64   `json:"load15"`
	NetIn       uint64    `json:"net_in"`
	NetOut      uint64    `json:"net_out"`
}

// WebSocket message structure
type Message struct {
	Type      string     `json:"type"`
	Password  string     `json:"password"`
	Data      SystemInfo `json:"data"`
	Timestamp time.Time  `json:"timestamp"`
}

var (
	isClient   = flag.Bool("c", false, "Run as client")
	isServer   = flag.Bool("s", false, "Run as server")
	configFile = flag.String("f", "", "Configuration file path")
	db         *gorm.DB
	upgrader   = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for simplicity
		},
	}
)

func main() {
	flag.Parse()

	if !*isClient && !*isServer {
		fmt.Println("Please specify either -c (client) or -s (server)")
		flag.Usage()
		os.Exit(1)
	}

	if *configFile == "" {
		if *isServer {
			*configFile = "server.json"
		} else {
			*configFile = "client.json"
		}
	}

	if *isServer {
		runServer()
	} else {
		runClient()
	}
}

func runServer() {
	fmt.Println("Starting server mode...")

	config, err := loadServerConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load server config: %v", err)
	}

	// Initialize database
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "server.db"
	}
	initDatabase(dbPath)

	// Setup WebSocket handler
	http.HandleFunc(config.WebSocket.Path, func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(w, r, config.Password)
	})

	// Start HTTP server
	addr := fmt.Sprintf("%s:%s", config.Listen, config.Port)
	fmt.Printf("Server listening on %s%s\n", addr, config.WebSocket.Path)
	log.Fatal(http.ListenAndServe(addr, nil))
}

func runClient() {
	fmt.Println("Starting client mode...")

	config, err := loadClientConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load client config: %v", err)
	}

	// Initialize local database
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "client.db"
	}
	initDatabase(dbPath)

	// Connect to server and start monitoring
	connectAndMonitor(config)
}

func loadServerConfig(filename string) (*ServerConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ServerConfig
	err = json.Unmarshal(data, &config)
	return &config, err
}

func loadClientConfig(filename string) (*ClientConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ClientConfig
	err = json.Unmarshal(data, &config)
	return &config, err
}

func initDatabase(dbPath string) {
	var err error
	db, err = gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        dbPath,
	}, &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Auto migrate the schema
	err = db.AutoMigrate(&SystemInfo{})
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	fmt.Printf("Database initialized: %s\n", dbPath)
}

func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	fmt.Printf("Client connected: %s\n", r.RemoteAddr)

	for {
		var msg Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("Read error: %v", err)
			break
		}

		// Verify password
		if msg.Password != password {
			log.Printf("Invalid password from %s", r.RemoteAddr)
			continue
		}

		// Store data in database
		msg.Data.Timestamp = time.Now()
		result := db.Create(&msg.Data)
		if result.Error != nil {
			log.Printf("Database error: %v", result.Error)
		} else {
			fmt.Printf("Stored data from %s: CPU=%.2f%%, Mem=%.2f%%\n",
				msg.Data.Hostname, msg.Data.CPU, msg.Data.MemPercent)
		}
	}

	fmt.Printf("Client disconnected: %s\n", r.RemoteAddr)
}

func connectAndMonitor(config *ClientConfig) {
	url := fmt.Sprintf("ws://%s:%s%s", config.Server, config.Port, config.WebSocket.Path)

	for {
		fmt.Printf("Connecting to %s...\n", url)

		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			log.Printf("Connection failed: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		fmt.Println("Connected to server")

		// Start monitoring loop
		monitorLoop(conn, config.Password)

		conn.Close()
		fmt.Println("Disconnected, retrying in 5 seconds...")
		time.Sleep(5 * time.Second)
	}
}

func monitorLoop(conn *websocket.Conn, password string) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sysInfo, err := collectSystemInfo()
			if err != nil {
				log.Printf("Failed to collect system info: %v", err)
				continue
			}

			// Store locally
			sysInfo.Timestamp = time.Now()
			db.Create(&sysInfo)

			// Send to server
			msg := Message{
				Type:      "system_info",
				Password:  password,
				Data:      *sysInfo,
				Timestamp: time.Now(),
			}

			err = conn.WriteJSON(msg)
			if err != nil {
				log.Printf("Send error: %v", err)
				return
			}

			fmt.Printf("Sent: CPU=%.2f%%, Mem=%.2f%%, Disk=%.2f%%\n",
				sysInfo.CPU, sysInfo.MemPercent, sysInfo.DiskPercent)
		}
	}
}

func collectSystemInfo() (*SystemInfo, error) {
	sysInfo := &SystemInfo{}

	// Get hostname
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("failed to get host info: %v", err)
	}
	sysInfo.Hostname = hostInfo.Hostname
	sysInfo.Platform = hostInfo.Platform

	// Get CPU usage
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get CPU usage: %v", err)
	}
	if len(cpuPercent) > 0 {
		sysInfo.CPU = cpuPercent[0]
	}

	// Get memory usage
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get memory info: %v", err)
	}
	sysInfo.MemTotal = memInfo.Total
	sysInfo.MemUsed = memInfo.Used
	sysInfo.MemPercent = memInfo.UsedPercent

	// Get disk usage (root partition)
	diskInfo, err := disk.Usage("/")
	if err != nil {
		// Try Windows C: drive
		diskInfo, err = disk.Usage("C:")
		if err != nil {
			return nil, fmt.Errorf("failed to get disk info: %v", err)
		}
	}
	sysInfo.DiskTotal = diskInfo.Total
	sysInfo.DiskUsed = diskInfo.Used
	sysInfo.DiskPercent = diskInfo.UsedPercent

	// Get load average
	loadInfo, err := load.Avg()
	if err == nil {
		sysInfo.Load1 = loadInfo.Load1
		sysInfo.Load5 = loadInfo.Load5
		sysInfo.Load15 = loadInfo.Load15
	}

	// Get network I/O
	netInfo, err := net.IOCounters(false)
	if err == nil && len(netInfo) > 0 {
		sysInfo.NetIn = netInfo[0].BytesRecv
		sysInfo.NetOut = netInfo[0].BytesSent
	}

	return sysInfo, nil
}
