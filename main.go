package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"

	"github.com/gofiber/fiber/v2"
)

// Host struct matches the "host" field in the JSON
type Host struct {
	Platform        string   `json:"Platform"`
	PlatformVersion string   `json:"PlatformVersion"`
	CPU             []string `json:"CPU"`
	MemTotal        int64    `json:"MemTotal"`
	DiskTotal       int64    `json:"DiskTotal"`
	SwapTotal       int64    `json:"SwapTotal"`
	Arch            string   `json:"Arch"`
	Virtualization  string   `json:"Virtualization"`
	BootTime        int64    `json:"BootTime"`
	CountryCode     string   `json:"CountryCode"`
	Version         string   `json:"Version"`
}

// Status struct matches the "status" field in the JSON
type Status struct {
	CPU            float64 `json:"CPU"`
	MemUsed        int64   `json:"MemUsed"`
	SwapUsed       int64   `json:"SwapUsed"`
	DiskUsed       int64   `json:"DiskUsed"`
	NetInTransfer  int64   `json:"NetInTransfer"`
	NetOutTransfer int64   `json:"NetOutTransfer"`
	NetInSpeed     int64   `json:"NetInSpeed"`
	NetOutSpeed    int64   `json:"NetOutSpeed"`
	Uptime         int64   `json:"Uptime"`
	Load1          float64 `json:"Load1"`
	Load5          float64 `json:"Load5"`
	Load15         float64 `json:"Load15"`
	TcpConnCount   int     `json:"TcpConnCount"`
	UdpConnCount   int     `json:"UdpConnCount"`
	ProcessCount   int     `json:"ProcessCount"`
}

// ServerDetails struct matches the overall JSON structure
type ServerDetails struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Tag        string `json:"tag"`
	LastActive int64  `json:"last_active"`
	IPv4       string `json:"ipv4"`
	IPv6       string `json:"ipv6"`
	ValidIP    string `json:"valid_ip"`
	Host       Host   `json:"host"`
	Status     Status `json:"status"`
}

var db *sql.DB

func main() {
	// Initialize database
	fmt.Println("Main: Initializing database...")
	initDB()
	fmt.Println("Main: Database initialization complete.")

	// Initialize Fiber app
	app := fiber.New()

	// Define API endpoint
	app.Get("/api/v1/server/details", getServerDetails)

	// Start server
	log.Fatal(app.Listen(":3000"))
}

func initDB() {
	var err error
	db, err = sql.Open("sqlite3", "./server_monitor.db")
	if err != nil {
		log.Printf("initDB: Error opening database: %v", err)
		return // Or handle error appropriately, e.g., os.Exit(1)
	}
	fmt.Println("initDB: Database opened successfully.")

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS server_details (
		id INTEGER PRIMARY KEY,
		name TEXT,
		tag TEXT,
		last_active INTEGER,
		ipv4 TEXT,
		ipv6 TEXT,
		valid_ip TEXT,
		host_platform TEXT,
		host_platform_version TEXT,
		host_cpu TEXT,
		host_mem_total INTEGER,
		host_disk_total INTEGER,
		host_swap_total INTEGER,
		host_arch TEXT,
		host_virtualization TEXT,
		host_boot_time INTEGER,
		host_country_code TEXT,
		host_version TEXT,
		status_cpu REAL,
		status_mem_used INTEGER,
		status_swap_used INTEGER,
		status_disk_used INTEGER,
		status_net_in_transfer INTEGER,
		status_net_out_transfer INTEGER,
		status_net_in_speed INTEGER,
		status_net_out_speed INTEGER,
		status_uptime INTEGER,
		status_load1 REAL,
		status_load5 REAL,
		status_load15 REAL,
		status_tcp_conn_count INTEGER,
		status_udp_conn_count INTEGER,
		status_process_count INTEGER
	);`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		log.Printf("initDB: Error creating table: %v", err)
		return
	}
	fmt.Println("initDB: Table 'server_details' created/verified.")

	// Insert example data if table is empty
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM server_details").Scan(&count)
	if err != nil {
		log.Printf("initDB: Error querying row count: %v", err)
		return
	}
	fmt.Printf("initDB: Current row count in 'server_details': %d\n", count)

	if count == 0 {
		insertExampleData()
	}
}

func insertExampleData() {
	exampleData := ServerDetails{
		ID:         1,
		Name:       "Server1",
		Tag:        "Tag1",
		LastActive: 1653015042,
		IPv4:       "*******",
		IPv6:       "",
		ValidIP:    "*******",
		Host: Host{
			Platform:        "darwin",
			PlatformVersion: "12.3.1",
			CPU:             []string{"Apple M1 Pro 1 Physical Core"},
			MemTotal:        17179869184,
			DiskTotal:       2473496842240,
			SwapTotal:       0,
			Arch:            "arm64",
			Virtualization:  "",
			BootTime:        1652683962,
			CountryCode:     "hk",
			Version:         "",
		},
		Status: Status{
			CPU:            17.33,
			MemUsed:        14013841408,
			SwapUsed:       0,
			DiskUsed:       2335048912896,
			NetInTransfer:  2710273234,
			NetOutTransfer: 695454765,
			NetInSpeed:     10806,
			NetOutSpeed:    5303,
			Uptime:         331080,
			Load1:          5.23,
			Load5:          4.87,
			Load15:         3.99,
			TcpConnCount:   195,
			UdpConnCount:   70,
			ProcessCount:   437,
		},
	}

	// Marshal CPU slice to JSON string for storage
	cpuJSON, err := json.Marshal(exampleData.Host.CPU)
	if err != nil {
		log.Printf("insertExampleData: Error marshaling CPU JSON: %v", err)
		return
	}

	insertSQL := `
	INSERT INTO server_details (
		id, name, tag, last_active, ipv4, ipv6, valid_ip,
		host_platform, host_platform_version, host_cpu, host_mem_total, host_disk_total, host_swap_total,
		host_arch, host_virtualization, host_boot_time, host_country_code, host_version,
		status_cpu, status_mem_used, status_swap_used, status_disk_used, status_net_in_transfer,
		status_net_out_transfer, status_net_in_speed, status_net_out_speed, status_uptime,
		status_load1, status_load5, status_load15, status_tcp_conn_count, status_udp_conn_count,
		status_process_count
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);`

	_, err = db.Exec(insertSQL,
		exampleData.ID, exampleData.Name, exampleData.Tag, exampleData.LastActive, exampleData.IPv4, exampleData.IPv6, exampleData.ValidIP,
		exampleData.Host.Platform, exampleData.Host.PlatformVersion, string(cpuJSON), exampleData.Host.MemTotal, exampleData.Host.DiskTotal, exampleData.Host.SwapTotal,
		exampleData.Host.Arch, exampleData.Host.Virtualization, exampleData.Host.BootTime, exampleData.Host.CountryCode, exampleData.Host.Version,
		exampleData.Status.CPU, exampleData.Status.MemUsed, exampleData.Status.SwapUsed, exampleData.Status.DiskUsed, exampleData.Status.NetInTransfer,
		exampleData.Status.NetOutTransfer, exampleData.Status.NetInSpeed, exampleData.Status.NetOutSpeed, exampleData.Status.Uptime,
		exampleData.Status.Load1, exampleData.Status.Load5, exampleData.Status.Load15, exampleData.Status.TcpConnCount, exampleData.Status.UdpConnCount,
		exampleData.Status.ProcessCount,
	)
	if err != nil {
		log.Printf("insertExampleData: Error inserting example data: %v", err)
		return
	}
	fmt.Println("insertExampleData: Example data inserted successfully.")
}

func getServerDetails(c *fiber.Ctx) error {
	row := db.QueryRow("SELECT * FROM server_details WHERE id = ?", 1) // Assuming ID 1 for now

	var (
		id                                                                                                                                          int
		name, tag, ipv4, ipv6, validIP                                                                                                              string
		lastActive                                                                                                                                  int64
		hostPlatform, hostPlatformVersion, hostCPU, hostArch, hostVirtualization, hostCountryCode, hostVersion                                      string
		hostMemTotal, hostDiskTotal, hostSwapTotal, hostBootTime                                                                                    int64
		statusCPU, statusLoad1, statusLoad5, statusLoad15                                                                                           float64
		statusMemUsed, statusSwapUsed, statusDiskUsed, statusNetInTransfer, statusNetOutTransfer, statusNetInSpeed, statusNetOutSpeed, statusUptime int64
		statusTcpConnCount, statusUdpConnCount, statusProcessCount                                                                                  int
	)

	err := row.Scan(
		&id, &name, &tag, &lastActive, &ipv4, &ipv6, &validIP,
		&hostPlatform, &hostPlatformVersion, &hostCPU, &hostMemTotal, &hostDiskTotal, &hostSwapTotal,
		&hostArch, &hostVirtualization, &hostBootTime, &hostCountryCode, &hostVersion,
		&statusCPU, &statusMemUsed, &statusSwapUsed, &statusDiskUsed, &statusNetInTransfer,
		&statusNetOutTransfer, &statusNetInSpeed, &statusNetOutSpeed, &statusUptime,
		&statusLoad1, &statusLoad5, &statusLoad15, &statusTcpConnCount, &statusUdpConnCount,
		&statusProcessCount,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Server details not found"})
		}
		log.Printf("Error scanning row: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Internal server error"})
	}

	// Unmarshal CPU string back to slice
	var cpuSlice []string
	err = json.Unmarshal([]byte(hostCPU), &cpuSlice)
	if err != nil {
		log.Printf("Error unmarshaling CPU: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Internal server error"})
	}

	serverDetails := ServerDetails{
		ID:         id,
		Name:       name,
		Tag:        tag,
		LastActive: lastActive,
		IPv4:       ipv4,
		IPv6:       ipv6,
		ValidIP:    validIP,
		Host: Host{
			Platform:        hostPlatform,
			PlatformVersion: hostPlatformVersion,
			CPU:             cpuSlice,
			MemTotal:        hostMemTotal,
			DiskTotal:       hostDiskTotal,
			SwapTotal:       hostSwapTotal,
			Arch:            hostArch,
			Virtualization:  hostVirtualization,
			BootTime:        hostBootTime,
			CountryCode:     hostCountryCode,
			Version:         hostVersion,
		},
		Status: Status{
			CPU:            statusCPU,
			MemUsed:        statusMemUsed,
			SwapUsed:       statusSwapUsed,
			DiskUsed:       statusDiskUsed,
			NetInTransfer:  statusNetInTransfer,
			NetOutTransfer: statusNetOutTransfer,
			NetInSpeed:     statusNetInSpeed,
			NetOutSpeed:    statusNetOutSpeed,
			Uptime:         statusUptime,
			Load1:          statusLoad1,
			Load5:          statusLoad5,
			Load15:         statusLoad15,
			TcpConnCount:   statusTcpConnCount,
			UdpConnCount:   statusUdpConnCount,
			ProcessCount:   statusProcessCount,
		},
	}

	return c.JSON(serverDetails)
}
