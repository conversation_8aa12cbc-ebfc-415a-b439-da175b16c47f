package main

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/gofiber/fiber/v2"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	_ "modernc.org/sqlite"
)

// Host struct matches the "host" field in the JSON
type Host struct {
	Platform        string   `json:"Platform"`
	PlatformVersion string   `json:"PlatformVersion"`
	CPU             []string `json:"CPU" gorm:"-"`
	CPUJson         string   `json:"-" gorm:"column:host_cpu"`
	MemTotal        int64    `json:"MemTotal"`
	DiskTotal       int64    `json:"DiskTotal"`
	SwapTotal       int64    `json:"SwapTotal"`
	Arch            string   `json:"Arch"`
	Virtualization  string   `json:"Virtualization"`
	BootTime        int64    `json:"BootTime"`
	CountryCode     string   `json:"CountryCode"`
	Version         string   `json:"Version"`
}

// Status struct matches the "status" field in the JSON
type Status struct {
	CPU            float64 `json:"CPU"`
	MemUsed        int64   `json:"MemUsed"`
	SwapUsed       int64   `json:"SwapUsed"`
	DiskUsed       int64   `json:"DiskUsed"`
	NetInTransfer  int64   `json:"NetInTransfer"`
	NetOutTransfer int64   `json:"NetOutTransfer"`
	NetInSpeed     int64   `json:"NetInSpeed"`
	NetOutSpeed    int64   `json:"NetOutSpeed"`
	Uptime         int64   `json:"Uptime"`
	Load1          float64 `json:"Load1"`
	Load5          float64 `json:"Load5"`
	Load15         float64 `json:"Load15"`
	TcpConnCount   int     `json:"TcpConnCount"`
	UdpConnCount   int     `json:"UdpConnCount"`
	ProcessCount   int     `json:"ProcessCount"`
}

// ServerDetails struct for GORM
type ServerDetails struct {
	ID         int    `json:"id" gorm:"primaryKey"`
	Name       string `json:"name"`
	Tag        string `json:"tag"`
	LastActive int64  `json:"last_active"`
	IPv4       string `json:"ipv4"`
	IPv6       string `json:"ipv6"`
	ValidIP    string `json:"valid_ip"`

	// Host fields flattened
	HostPlatform        string `json:"-" gorm:"column:host_platform"`
	HostPlatformVersion string `json:"-" gorm:"column:host_platform_version"`
	HostCPU             string `json:"-" gorm:"column:host_cpu"`
	HostMemTotal        int64  `json:"-" gorm:"column:host_mem_total"`
	HostDiskTotal       int64  `json:"-" gorm:"column:host_disk_total"`
	HostSwapTotal       int64  `json:"-" gorm:"column:host_swap_total"`
	HostArch            string `json:"-" gorm:"column:host_arch"`
	HostVirtualization  string `json:"-" gorm:"column:host_virtualization"`
	HostBootTime        int64  `json:"-" gorm:"column:host_boot_time"`
	HostCountryCode     string `json:"-" gorm:"column:host_country_code"`
	HostVersion         string `json:"-" gorm:"column:host_version"`

	// Status fields flattened
	StatusCPU            float64 `json:"-" gorm:"column:status_cpu"`
	StatusMemUsed        int64   `json:"-" gorm:"column:status_mem_used"`
	StatusSwapUsed       int64   `json:"-" gorm:"column:status_swap_used"`
	StatusDiskUsed       int64   `json:"-" gorm:"column:status_disk_used"`
	StatusNetInTransfer  int64   `json:"-" gorm:"column:status_net_in_transfer"`
	StatusNetOutTransfer int64   `json:"-" gorm:"column:status_net_out_transfer"`
	StatusNetInSpeed     int64   `json:"-" gorm:"column:status_net_in_speed"`
	StatusNetOutSpeed    int64   `json:"-" gorm:"column:status_net_out_speed"`
	StatusUptime         int64   `json:"-" gorm:"column:status_uptime"`
	StatusLoad1          float64 `json:"-" gorm:"column:status_load1"`
	StatusLoad5          float64 `json:"-" gorm:"column:status_load5"`
	StatusLoad15         float64 `json:"-" gorm:"column:status_load15"`
	StatusTcpConnCount   int     `json:"-" gorm:"column:status_tcp_conn_count"`
	StatusUdpConnCount   int     `json:"-" gorm:"column:status_udp_conn_count"`
	StatusProcessCount   int     `json:"-" gorm:"column:status_process_count"`

	// Nested structs for JSON response
	Host   Host   `json:"host" gorm:"-"`
	Status Status `json:"status" gorm:"-"`
}

// TableName specifies the table name for GORM
func (ServerDetails) TableName() string {
	return "server_details"
}

var db *gorm.DB

func main() {
	// Initialize database
	fmt.Println("Main: Initializing database...")
	initDB()
	fmt.Println("Main: Database initialization complete.")

	// Initialize Fiber app
	app := fiber.New()

	// Define API endpoint
	app.Get("/api/v1/server/details", getServerDetails)

	// Start server
	log.Fatal(app.Listen(":3000"))
}

func initDB() {
	var err error
	db, err = gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        "./server_monitor.db",
	}, &gorm.Config{})
	if err != nil {
		log.Printf("initDB: Error opening database: %v", err)
		return
	}
	fmt.Println("initDB: Database opened successfully.")

	// Auto migrate the schema
	err = db.AutoMigrate(&ServerDetails{})
	if err != nil {
		log.Printf("initDB: Error migrating schema: %v", err)
		return
	}
	fmt.Println("initDB: Schema migrated successfully.")

	// Check if table has data
	var count int64
	db.Model(&ServerDetails{}).Count(&count)
	fmt.Printf("initDB: Current row count in 'server_details': %d\n", count)

	if count == 0 {
		insertExampleData()
	}
}

func insertExampleData() {
	cpuSlice := []string{"Apple M1 Pro 1 Physical Core"}
	cpuJSON, err := json.Marshal(cpuSlice)
	if err != nil {
		log.Printf("insertExampleData: Error marshaling CPU JSON: %v", err)
		return
	}

	exampleData := ServerDetails{
		ID:         1,
		Name:       "Server1",
		Tag:        "Tag1",
		LastActive: 1653015042,
		IPv4:       "*******",
		IPv6:       "",
		ValidIP:    "*******",

		// Host fields
		HostPlatform:        "darwin",
		HostPlatformVersion: "12.3.1",
		HostCPU:             string(cpuJSON),
		HostMemTotal:        17179869184,
		HostDiskTotal:       2473496842240,
		HostSwapTotal:       0,
		HostArch:            "arm64",
		HostVirtualization:  "",
		HostBootTime:        1652683962,
		HostCountryCode:     "hk",
		HostVersion:         "",

		// Status fields
		StatusCPU:            17.33,
		StatusMemUsed:        14013841408,
		StatusSwapUsed:       0,
		StatusDiskUsed:       2335048912896,
		StatusNetInTransfer:  2710273234,
		StatusNetOutTransfer: 695454765,
		StatusNetInSpeed:     10806,
		StatusNetOutSpeed:    5303,
		StatusUptime:         331080,
		StatusLoad1:          5.23,
		StatusLoad5:          4.87,
		StatusLoad15:         3.99,
		StatusTcpConnCount:   195,
		StatusUdpConnCount:   70,
		StatusProcessCount:   437,
	}

	result := db.Create(&exampleData)
	if result.Error != nil {
		log.Printf("insertExampleData: Error inserting example data: %v", result.Error)
		return
	}
	fmt.Println("insertExampleData: Example data inserted successfully.")
}

func getServerDetails(c *fiber.Ctx) error {
	var serverDetails ServerDetails

	result := db.First(&serverDetails, 1) // Get record with ID 1
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Server details not found"})
		}
		log.Printf("Error querying database: %v", result.Error)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Internal server error"})
	}

	// Unmarshal CPU string back to slice
	var cpuSlice []string
	err := json.Unmarshal([]byte(serverDetails.HostCPU), &cpuSlice)
	if err != nil {
		log.Printf("Error unmarshaling CPU: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Internal server error"})
	}

	// Populate nested structs for JSON response
	serverDetails.Host = Host{
		Platform:        serverDetails.HostPlatform,
		PlatformVersion: serverDetails.HostPlatformVersion,
		CPU:             cpuSlice,
		MemTotal:        serverDetails.HostMemTotal,
		DiskTotal:       serverDetails.HostDiskTotal,
		SwapTotal:       serverDetails.HostSwapTotal,
		Arch:            serverDetails.HostArch,
		Virtualization:  serverDetails.HostVirtualization,
		BootTime:        serverDetails.HostBootTime,
		CountryCode:     serverDetails.HostCountryCode,
		Version:         serverDetails.HostVersion,
	}

	serverDetails.Status = Status{
		CPU:            serverDetails.StatusCPU,
		MemUsed:        serverDetails.StatusMemUsed,
		SwapUsed:       serverDetails.StatusSwapUsed,
		DiskUsed:       serverDetails.StatusDiskUsed,
		NetInTransfer:  serverDetails.StatusNetInTransfer,
		NetOutTransfer: serverDetails.StatusNetOutTransfer,
		NetInSpeed:     serverDetails.StatusNetInSpeed,
		NetOutSpeed:    serverDetails.StatusNetOutSpeed,
		Uptime:         serverDetails.StatusUptime,
		Load1:          serverDetails.StatusLoad1,
		Load5:          serverDetails.StatusLoad5,
		Load15:         serverDetails.StatusLoad15,
		TcpConnCount:   serverDetails.StatusTcpConnCount,
		UdpConnCount:   serverDetails.StatusUdpConnCount,
		ProcessCount:   serverDetails.StatusProcessCount,
	}

	return c.JSON(serverDetails)
}
