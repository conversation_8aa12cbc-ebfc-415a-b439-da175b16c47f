<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Monitor Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .servers-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .server-card {
            background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2); transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .server-card:hover { transform: translateY(-5px); box-shadow: 0 12px 40px rgba(0,0,0,0.15); }
        .server-header {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #f0f0f0;
        }
        .server-name { font-size: 1.4rem; font-weight: bold; color: #333; }
        .server-id { background: #667eea; color: white; padding: 5px 12px; border-radius: 20px; font-size: 0.9rem; font-weight: bold; }
        .server-tag { background: #28a745; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem; margin-left: 10px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px; }
        .metric { text-align: center; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea; }
        .metric-value { font-size: 1.8rem; font-weight: bold; color: #333; margin-bottom: 5px; }
        .metric-label { font-size: 0.9rem; color: #666; text-transform: uppercase; letter-spacing: 0.5px; }
        .progress-bar { width: 100%; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin-top: 8px; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); border-radius: 4px; transition: width 0.3s ease; }
        .progress-fill.warning { background: linear-gradient(90deg, #ffc107, #fd7e14); }
        .progress-fill.danger { background: linear-gradient(90deg, #dc3545, #e83e8c); }
        .server-info { background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 15px; }
        .info-row { display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 0.9rem; }
        .info-label { color: #666; font-weight: 500; }
        .info-value { color: #333; font-weight: bold; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #28a745; box-shadow: 0 0 10px rgba(40, 167, 69, 0.5); }
        .status-offline { background: #dc3545; }
        .last-update { text-align: center; color: white; font-size: 0.9rem; opacity: 0.8; margin-top: 20px; }
        .loading { text-align: center; color: white; font-size: 1.2rem; margin-top: 50px; }
        .error { background: rgba(220, 53, 69, 0.9); color: white; padding: 15px; border-radius: 10px; text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ Server Monitor Dashboard</h1>
            <p>Real-time monitoring with AES-128-GCM encryption</p>
        </div>
        <div id="loading" class="loading">Loading server data...</div>
        <div id="error" class="error" style="display: none;">Failed to load server data. Please check if the server is running.</div>
        <div id="servers-container" class="servers-grid" style="display: none;"></div>
        <div id="last-update" class="last-update" style="display: none;">Last updated: <span id="update-time"></span></div>
    </div>

    <script>
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            if (days > 0) return `${days}d ${hours}h ${minutes}m`;
            if (hours > 0) return `${hours}h ${minutes}m`;
            return `${minutes}m`;
        }

        function getProgressBarClass(percentage) {
            if (percentage > 80) return 'danger';
            if (percentage > 60) return 'warning';
            return '';
        }

        function isServerOnline(lastActive) {
            const now = Math.floor(Date.now() / 1000);
            return (now - lastActive) < 60;
        }

        function createServerCard(server) {
            const isOnline = isServerOnline(server.last_active);
            const cpuPercent = server.status.CPU.toFixed(1);
            const memPercent = ((server.status.MemUsed / server.host.MemTotal) * 100).toFixed(1);
            const diskPercent = ((server.status.DiskUsed / server.host.DiskTotal) * 100).toFixed(1);

            return `
                <div class="server-card">
                    <div class="server-header">
                        <div>
                            <span class="status-indicator ${isOnline ? 'status-online' : 'status-offline'}"></span>
                            <span class="server-name">${server.name}</span>
                            <span class="server-tag">${server.tag}</span>
                        </div>
                        <div class="server-id">MID: ${server.id}</div>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric">
                            <div class="metric-value">${cpuPercent}%</div>
                            <div class="metric-label">CPU Usage</div>
                            <div class="progress-bar">
                                <div class="progress-fill ${getProgressBarClass(cpuPercent)}" style="width: ${cpuPercent}%"></div>
                            </div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${memPercent}%</div>
                            <div class="metric-label">Memory Usage</div>
                            <div class="progress-bar">
                                <div class="progress-fill ${getProgressBarClass(memPercent)}" style="width: ${memPercent}%"></div>
                            </div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${diskPercent}%</div>
                            <div class="metric-label">Disk Usage</div>
                            <div class="progress-bar">
                                <div class="progress-fill ${getProgressBarClass(diskPercent)}" style="width: ${diskPercent}%"></div>
                            </div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${formatUptime(server.status.Uptime)}</div>
                            <div class="metric-label">Uptime</div>
                        </div>
                    </div>
                    <div class="server-info">
                        <div class="info-row">
                            <span class="info-label">Platform:</span>
                            <span class="info-value">${server.host.Platform} ${server.host.PlatformVersion}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">CPU:</span>
                            <span class="info-value">${server.host.CPU}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Memory:</span>
                            <span class="info-value">${formatBytes(server.status.MemUsed)} / ${formatBytes(server.host.MemTotal)}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Disk:</span>
                            <span class="info-value">${formatBytes(server.status.DiskUsed)} / ${formatBytes(server.host.DiskTotal)}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">IP Address:</span>
                            <span class="info-value">${server.valid_ip}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Load Average:</span>
                            <span class="info-value">${server.status.Load1.toFixed(2)}, ${server.status.Load5.toFixed(2)}, ${server.status.Load15.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadServerData() {
            fetch('/api/servers')
                .then(response => response.json())
                .then(servers => {
                    const container = document.getElementById('servers-container');
                    const loading = document.getElementById('loading');
                    const error = document.getElementById('error');
                    const lastUpdate = document.getElementById('last-update');
                    const updateTime = document.getElementById('update-time');

                    loading.style.display = 'none';
                    error.style.display = 'none';
                    
                    if (servers.length === 0) {
                        container.innerHTML = '<div class="error">No servers found. Make sure clients are connected.</div>';
                    } else {
                        container.innerHTML = servers.map(createServerCard).join('');
                    }
                    
                    container.style.display = 'grid';
                    lastUpdate.style.display = 'block';
                    updateTime.textContent = new Date().toLocaleString();
                })
                .catch(err => {
                    console.error('Error loading server data:', err);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'block';
                });
        }

        loadServerData();
        setInterval(loadServerData, 5000);
    </script>
</body>
</html>
